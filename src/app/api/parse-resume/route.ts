import { NextRequest, NextResponse } from 'next/server';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    let text = '';

    // Parse based on file type
    if (file.type === 'application/pdf') {
      const pdfData = await pdfParse(buffer);
      text = pdfData.text;
    } else if (file.type.includes('word') || file.name.endsWith('.docx')) {
      const result = await mammoth.extractRawText({ buffer });
      text = result.value;
    } else if (file.type === 'text/plain') {
      text = buffer.toString('utf-8');
    } else {
      return NextResponse.json({ error: 'Unsupported file type' }, { status: 400 });
    }

    // Basic parsing to extract structured data
    const structured = parseResumeText(text);

    return NextResponse.json({
      text,
      structured,
      success: true
    });
  } catch (error) {
    console.error('Error parsing resume:', error);
    return NextResponse.json(
      { error: 'Failed to parse resume' },
      { status: 500 }
    );
  }
}

function parseResumeText(text: string) {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  
  const result = {
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      website: '',
      linkedin: ''
    },
    summary: '',
    experience: [],
    education: [],
    skills: []
  };

  // Extract email
  const emailMatch = text.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/);
  if (emailMatch) {
    result.personalInfo.email = emailMatch[0];
  }

  // Extract phone
  const phoneMatch = text.match(/(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/);
  if (phoneMatch) {
    result.personalInfo.phone = phoneMatch[0];
  }

  // Extract name (usually first line or line before email)
  if (lines.length > 0) {
    // Try to find name - usually the first substantial line
    for (const line of lines.slice(0, 5)) {
      if (line.length > 5 && line.length < 50 && 
          !line.includes('@') && 
          !line.match(/\d{3}/) && 
          line.split(' ').length >= 2) {
        result.personalInfo.fullName = line;
        break;
      }
    }
  }

  // Extract LinkedIn
  const linkedinMatch = text.match(/linkedin\.com\/in\/[\w-]+/i);
  if (linkedinMatch) {
    result.personalInfo.linkedin = linkedinMatch[0];
  }

  // Extract skills (look for skills section)
  const skillsSection = extractSection(text, ['skills', 'technical skills', 'core competencies']);
  if (skillsSection) {
    const skillsText = skillsSection.replace(/[•\-\*]/g, ',');
    result.skills = skillsText.split(/[,\n]/)
      .map(skill => skill.trim())
      .filter(skill => skill.length > 1 && skill.length < 30)
      .slice(0, 20); // Limit to 20 skills
  }

  return result;
}

function extractSection(text: string, sectionNames: string[]): string {
  const lines = text.split('\n');
  
  for (const sectionName of sectionNames) {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].toLowerCase().includes(sectionName.toLowerCase())) {
        // Found section header, extract content until next section
        const sectionContent = [];
        for (let j = i + 1; j < lines.length; j++) {
          const line = lines[j].trim();
          if (line.length === 0) continue;
          
          // Stop if we hit another section header
          if (isLikelySectionHeader(line)) break;
          
          sectionContent.push(line);
          if (sectionContent.length > 10) break; // Limit section size
        }
        return sectionContent.join('\n');
      }
    }
  }
  
  return '';
}

function isLikelySectionHeader(line: string): boolean {
  const commonHeaders = [
    'experience', 'education', 'skills', 'summary', 'objective',
    'work experience', 'employment', 'qualifications', 'certifications',
    'projects', 'achievements', 'awards'
  ];
  
  const lowerLine = line.toLowerCase();
  return commonHeaders.some(header => lowerLine.includes(header)) && line.length < 50;
}
