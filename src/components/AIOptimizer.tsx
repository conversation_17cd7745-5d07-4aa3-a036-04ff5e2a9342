'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FiUpload, FiZap, FiFileText, FiTarget, FiTrendingUp, FiCheckCircle, FiAlertCircle } from 'react-icons/fi';
import { FileUploader } from './FileUploader';
import { ApiKeyManager } from './ApiKeyManager';
import { SkillsVisualization } from './SkillsVisualization';
import { apiKeyManager } from '@/utils/apiKeyManager';
import toast, { Toaster } from 'react-hot-toast';
import Link from 'next/link';

interface OptimizationResult {
  optimizedResume: string;
  matchScore: number;
  changes: string[];
  matchingSkills: string[];
  missingSkills: string[];
  suggestions: string[];
}

export function AIOptimizer() {
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [resumeText, setResumeText] = useState('');
  const [jobDescription, setJobDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<OptimizationResult | null>(null);
  const [hasApiKey, setHasApiKey] = useState(false);

  useEffect(() => {
    setHasApiKey(apiKeyManager.hasApiKey());
  }, []);

  const handleFileSelect = async (file: File | null) => {
    setResumeFile(file);
    if (file) {
      setLoading(true);
      try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/api/parse-resume', {
          method: 'POST',
          body: formData,
        });
        
        if (!response.ok) {
          throw new Error('Failed to parse resume');
        }
        
        const data = await response.json();
        setResumeText(data.text);
        toast.success('Resume uploaded and parsed successfully!');
      } catch (error) {
        console.error('Error parsing resume:', error);
        toast.error('Failed to parse resume. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleOptimize = async () => {
    if (!resumeText.trim()) {
      toast.error('Please upload a resume first');
      return;
    }
    
    if (!jobDescription.trim()) {
      toast.error('Please enter a job description');
      return;
    }

    if (!hasApiKey) {
      toast.error('Please set up your OpenAI API key first');
      return;
    }

    setLoading(true);
    try {
      // This would call your AI optimization API
      toast.info('AI optimization feature coming soon!');
      
      // Mock result for now
      setTimeout(() => {
        setResult({
          optimizedResume: resumeText,
          matchScore: 85,
          changes: ['Added relevant keywords', 'Improved formatting', 'Enhanced skill descriptions'],
          matchingSkills: ['JavaScript', 'React', 'Node.js'],
          missingSkills: ['TypeScript', 'AWS', 'Docker'],
          suggestions: [
            'Add more quantifiable achievements',
            'Include relevant certifications',
            'Highlight leadership experience'
          ]
        });
        setLoading(false);
        toast.success('Resume optimization completed!');
      }, 2000);
    } catch (error) {
      console.error('Error optimizing resume:', error);
      toast.error('Failed to optimize resume. Please try again.');
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <Toaster position="top-right" />
      
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              AI Resume Optimizer
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Optimize your resume with AI-powered analysis and get personalized suggestions to match job requirements
            </p>
          </motion.div>
        </div>

        {/* Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg shadow-lg p-2 flex gap-2">
            <Link
              href="/builder"
              className="px-4 py-2 text-gray-600 hover:text-blue-600 rounded-lg transition-colors"
            >
              Resume Builder
            </Link>
            <div className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium">
              AI Optimizer
            </div>
            <Link
              href="/autofill"
              className="px-4 py-2 text-gray-600 hover:text-blue-600 rounded-lg transition-colors"
            >
              Auto-Fill
            </Link>
          </div>
        </div>

        {/* API Key Manager */}
        {!hasApiKey && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-8"
          >
            <ApiKeyManager onApiKeySet={() => setHasApiKey(true)} />
          </motion.div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Left Column - Input */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="space-y-6"
          >
            {/* Resume Upload */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <FiUpload className="w-5 h-5 text-blue-600" />
                Upload Your Resume
              </h2>
              <FileUploader
                onFileSelect={handleFileSelect}
                selectedFile={resumeFile}
                accept=".pdf,.doc,.docx,.txt"
              />
              {loading && (
                <div className="mt-4 text-center">
                  <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-600 bg-blue-100">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </div>
                </div>
              )}
            </div>

            {/* Job Description */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <FiTarget className="w-5 h-5 text-blue-600" />
                Target Job Description
              </h2>
              <textarea
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Paste the job description here to get AI-powered optimization suggestions..."
              />
            </div>

            {/* Optimize Button */}
            <button
              onClick={handleOptimize}
              disabled={loading || !hasApiKey}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 px-6 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              <FiZap className="w-5 h-5" />
              {loading ? 'Optimizing...' : 'Optimize Resume with AI'}
            </button>
          </motion.div>

          {/* Right Column - Results */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            {result ? (
              <>
                {/* Match Score */}
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FiTrendingUp className="w-5 h-5 text-green-600" />
                    Match Score
                  </h3>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-green-600 mb-2">{result.matchScore}%</div>
                    <p className="text-gray-600">Job Match Score</p>
                  </div>
                </div>

                {/* Skills Analysis */}
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Skills Analysis</h3>
                  <SkillsVisualization
                    matchingSkills={result.matchingSkills}
                    missingSkills={result.missingSkills}
                    matchScore={result.matchScore}
                  />
                </div>

                {/* Suggestions */}
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FiCheckCircle className="w-5 h-5 text-blue-600" />
                    AI Suggestions
                  </h3>
                  <div className="space-y-3">
                    {result.suggestions.map((suggestion, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                        <FiAlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                        <p className="text-gray-700">{suggestion}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <FiFileText className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Ready to Optimize
                </h3>
                <p className="text-gray-600">
                  Upload your resume and enter a job description to get AI-powered optimization suggestions.
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
